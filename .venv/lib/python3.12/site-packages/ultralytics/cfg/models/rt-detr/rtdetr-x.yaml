# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Ultralytics RT-DETR-x hybrid object detection model with P3/8 - P5/32 outputs
# Model docs: https://docs.ultralytics.com/models/rtdetr
# Task docs: https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  x: [1.00, 1.00, 2048]

backbone:
  # [from, repeats, module, args]
  - [-1, 1, HGStem, [32, 64]] # 0-P2/4
  - [-1, 6, H<PERSON><PERSON>, [64, 128, 3]] # stage 1

  - [-1, 1, DWConv, [128, 3, 2, 1, False]] # 2-P3/8
  - [-1, 6, H<PERSON><PERSON>, [128, 512, 3]]
  - [-1, 6, <PERSON><PERSON><PERSON>, [128, 512, 3, <PERSON>alse, True]] # 4-stage 2

  - [-1, 1, <PERSON><PERSON><PERSON><PERSON>, [512, 3, 2, 1, False]] # 5-P3/16
  - [-1, 6, H<PERSON><PERSON>, [256, 1024, 5, True, False]] # cm, c2, k, light, shortcut
  - [-1, 6, H<PERSON>lock, [256, 1024, 5, True, True]]
  - [-1, 6, HGBlock, [256, 1024, 5, True, True]]
  - [-1, 6, H<PERSON>lock, [256, 1024, 5, True, True]]
  - [-1, 6, H<PERSON>lock, [256, 1024, 5, True, True]] # 10-stage 3

  - [-1, 1, DWConv, [1024, 3, 2, 1, False]] # 11-P4/32
  - [-1, 6, HGBlock, [512, 2048, 5, True, False]]
  - [-1, 6, HGBlock, [512, 2048, 5, True, True]] # 13-stage 4

head:
  - [-1, 1, Conv, [384, 1, 1, None, 1, 1, False]] # 14 input_proj.2
  - [-1, 1, AIFI, [2048, 8]]
  - [-1, 1, Conv, [384, 1, 1]] # 16, Y5, lateral_convs.0

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [10, 1, Conv, [384, 1, 1, None, 1, 1, False]] # 18 input_proj.1
  - [[-2, -1], 1, Concat, [1]]
  - [-1, 3, RepC3, [384]] # 20, fpn_blocks.0
  - [-1, 1, Conv, [384, 1, 1]] # 21, Y4, lateral_convs.1

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [4, 1, Conv, [384, 1, 1, None, 1, 1, False]] # 23 input_proj.0
  - [[-2, -1], 1, Concat, [1]] # cat backbone P4
  - [-1, 3, RepC3, [384]] # X3 (25), fpn_blocks.1

  - [-1, 1, Conv, [384, 3, 2]] # 26, downsample_convs.0
  - [[-1, 21], 1, Concat, [1]] # cat Y4
  - [-1, 3, RepC3, [384]] # F4 (28), pan_blocks.0

  - [-1, 1, Conv, [384, 3, 2]] # 29, downsample_convs.1
  - [[-1, 16], 1, Concat, [1]] # cat Y5
  - [-1, 3, RepC3, [384]] # F5 (31), pan_blocks.1

  - [[25, 28, 31], 1, RTDETRDecoder, [nc]] # Detect(P3, P4, P5)
