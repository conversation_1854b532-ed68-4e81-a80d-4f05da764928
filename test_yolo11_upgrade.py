#!/usr/bin/env python3
"""
Test script to verify YOLO11 upgrade and face/chest detection improvements.
"""

import sys
import traceback
from pathlib import Path
import numpy as np
import cv2

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_yolo11_model_loading():
    """Test YOLO11 model loading with face detection priority."""
    print("Testing YOLO11 model loading...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.models.human_detector import HumanDetector
        
        # Load config
        config = Config()
        print(f"✓ Model path: {config.get('models.human_detection.model_path')}")
        print(f"✓ Device: {config.model_device}")
        print(f"✓ Face detection priority: {config.get('models.human_detection.face_detection_priority')}")
        print(f"✓ Confidence threshold: {config.model_confidence_threshold}")
        
        # Initialize detector (this will download YOLO11x-pose if needed)
        print("  Loading YOLO11x-pose model (this may take a moment for first download)...")
        detector = HumanDetector(config)
        
        print("✓ YOLO11x-pose model loaded successfully")
        return True
        
    except Exception as e:
        print(f"✗ Model loading error: {e}")
        traceback.print_exc()
        return False


def test_face_chest_centering():
    """Test the new face/chest centering algorithm."""
    print("\nTesting face/chest centering...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.image_processing.centering import PhotoCenterer
        
        config = Config()
        centerer = PhotoCenterer(config)
        
        # Test configuration values
        print(f"✓ Centering method: {config.get('centering.method')}")
        print(f"✓ Face weight: {config.face_weight}")
        print(f"✓ Chest weight: {config.chest_weight}")
        print(f"✓ Target position: {config.target_position}")
        print(f"✓ Margin ratio: {config.margin_ratio}")
        
        # Create test image
        test_image = np.zeros((600, 800, 3), dtype=np.uint8)
        test_image[200:400, 300:500] = [128, 128, 128]  # Gray rectangle
        
        # Mock detection with face and chest keypoints
        mock_detection = {
            'bbox': [300, 200, 500, 400],
            'confidence': 0.85,
            'center': (400, 300),
            'keypoints': {
                'nose': (400, 250),
                'left_eye': (390, 240),
                'right_eye': (410, 240),
                'left_shoulder': (380, 320),
                'right_shoulder': (420, 320)
            }
        }
        
        # Test face/chest centering
        result = centerer.center_subject(test_image, mock_detection)
        print(f"✓ Face/chest centering works: confidence={result.confidence:.3f}, method={result.method_used}")
        
        return True
        
    except Exception as e:
        print(f"✗ Face/chest centering error: {e}")
        traceback.print_exc()
        return False


def test_detection_with_real_image():
    """Test detection on a synthetic image with person-like features."""
    print("\nTesting detection with synthetic person image...")
    
    try:
        from src.photo_center.models.human_detector import HumanDetector
        from src.photo_center.utils.config import Config
        
        config = Config()
        detector = HumanDetector(config)
        
        # Create a more realistic synthetic person image
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Draw a simple person-like figure
        # Head (circle)
        cv2.circle(test_image, (320, 120), 40, (200, 180, 160), -1)
        # Body (rectangle)
        cv2.rectangle(test_image, (290, 160), (350, 300), (100, 150, 200), -1)
        # Arms
        cv2.rectangle(test_image, (250, 180), (290, 250), (100, 150, 200), -1)
        cv2.rectangle(test_image, (350, 180), (390, 250), (100, 150, 200), -1)
        # Legs
        cv2.rectangle(test_image, (300, 300), (320, 400), (50, 100, 150), -1)
        cv2.rectangle(test_image, (320, 300), (340, 400), (50, 100, 150), -1)
        
        # Add some noise to make it more realistic
        noise = np.random.randint(0, 50, test_image.shape, dtype=np.uint8)
        test_image = cv2.add(test_image, noise)
        
        # Test detection
        detections = detector.detect_humans(test_image)
        print(f"✓ Detection completed: found {len(detections)} humans")
        
        if detections:
            best_detection = detector.get_best_detection(detections)
            keypoints = best_detection.get('keypoints', {})
            print(f"✓ Best detection confidence: {best_detection['confidence']:.3f}")
            print(f"✓ Keypoints detected: {len(keypoints)}")
            
            # Check for face/chest keypoints specifically
            face_keypoints = ['nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear']
            chest_keypoints = ['left_shoulder', 'right_shoulder']
            
            face_count = sum(1 for kp in face_keypoints if kp in keypoints)
            chest_count = sum(1 for kp in chest_keypoints if kp in keypoints)
            
            print(f"✓ Face keypoints: {face_count}/5")
            print(f"✓ Chest keypoints: {chest_count}/2")
        
        return True
        
    except Exception as e:
        print(f"✗ Detection error: {e}")
        traceback.print_exc()
        return False


def test_gpu_utilization():
    """Test GPU utilization if CUDA is available."""
    print("\nTesting GPU utilization...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
            print(f"✓ CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
            
            # Test if model can use GPU
            from src.photo_center.utils.config import Config
            config = Config()
            device = config.model_device
            
            if device == 'cuda':
                print("✓ Model configured to use CUDA")
                return True
            else:
                print("⚠ Model configured for CPU (consider changing to 'cuda' in config)")
                return True
        else:
            print("⚠ CUDA not available - using CPU")
            return True
            
    except ImportError:
        print("⚠ PyTorch not available - cannot check GPU status")
        return True
    except Exception as e:
        print(f"✗ GPU test error: {e}")
        return False


def main():
    """Run all YOLO11 upgrade tests."""
    print("YOLO11 Upgrade Test Suite")
    print("=" * 50)
    
    tests = [
        test_gpu_utilization,
        test_yolo11_model_loading,
        test_face_chest_centering,
        test_detection_with_real_image,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except KeyboardInterrupt:
            print("\n\nTest interrupted by user")
            break
        except Exception as e:
            print(f"\n✗ Unexpected error in {test.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ All {total} tests passed!")
        print("\nYOLO11 upgrade successful!")
        print("Your photo centering system now uses:")
        print("- YOLO11x-pose for superior accuracy")
        print("- Face/chest prioritized detection")
        print("- GPU acceleration (if available)")
        print("- Enhanced keypoint-based centering")
        return 0
    else:
        print(f"✗ {total - passed} out of {total} tests failed")
        print("\nSome functionality may not work correctly.")
        print("Please check the error messages above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
