"""Core centering algorithms for photo centering."""

import cv2
import numpy as np
from typing import Dict, Any, Tu<PERSON>, Optional, List
from dataclasses import dataclass

from ..utils.logger import get_logger
from ..utils.config import Config


@dataclass
class CenteringResult:
    """Result of centering operation."""
    cropped_image: np.ndarray
    crop_box: Tuple[int, int, int, int]  # (x1, y1, x2, y2)
    subject_center: Tuple[int, int]
    target_center: Tuple[int, int]
    confidence: float
    method_used: str


class PhotoCenterer:
    """Core photo centering functionality."""
    
    def __init__(self, config: Optional[Config] = None):
        """Initialize photo centerer.
        
        Args:
            config: Configuration object. If None, creates default config.
        """
        self.config = config or Config()
        self.logger = get_logger(__name__)
    
    def center_subject(
        self,
        image: np.ndarray,
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center the detected subject in the image.

        Args:
            image: Input image as numpy array
            detection: Detection dictionary from human detector
            target_size: Target output size (width, height). If None, uses original size

        Returns:
            CenteringResult with cropped image and metadata
        """
        method = self.config.get('centering.method', 'keypoint_based')

        if method == 'face_chest_based' and detection.get('keypoints'):
            return self._center_by_face_chest(image, detection, target_size)
        elif method == 'keypoint_based' and detection.get('keypoints'):
            return self._center_by_keypoints(image, detection, target_size)
        elif method == 'bbox_based' or not detection.get('keypoints'):
            return self._center_by_bbox(image, detection, target_size)
        elif method == 'center_of_mass':
            return self._center_by_mass(image, detection, target_size)
        else:
            # Fallback to bbox method
            return self._center_by_bbox(image, detection, target_size)

    def _center_by_face_chest(
        self,
        image: np.ndarray,
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on face and chest keypoints with weighted priority.

        Args:
            image: Input image
            detection: Detection with keypoints
            target_size: Target output size

        Returns:
            CenteringResult
        """
        keypoints = detection.get('keypoints', {})
        if not keypoints:
            # Fallback to bbox method if no keypoints
            return self._center_by_bbox(image, detection, target_size)

        # Define face and chest keypoints
        face_keypoints = ['nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear']
        chest_keypoints = ['left_shoulder', 'right_shoulder']

        # Collect available face and chest points
        face_points = []
        chest_points = []

        for kp_name in face_keypoints:
            if kp_name in keypoints:
                face_points.append(keypoints[kp_name])

        for kp_name in chest_keypoints:
            if kp_name in keypoints:
                chest_points.append(keypoints[kp_name])

        # Calculate weighted center
        face_weight = self.config.get('centering.face_weight', 0.7)
        chest_weight = self.config.get('centering.chest_weight', 0.3)

        weighted_points = []
        weights = []

        # Add face points with higher weight
        for point in face_points:
            weighted_points.append(point)
            weights.append(face_weight / len(face_points) if face_points else 0)

        # Add chest points with lower weight
        for point in chest_points:
            weighted_points.append(point)
            weights.append(chest_weight / len(chest_points) if chest_points else 0)

        if not weighted_points:
            # No face or chest keypoints available, fallback to all keypoints
            return self._center_by_keypoints(image, detection, target_size)

        # Calculate weighted center
        weighted_points = np.array(weighted_points)
        weights = np.array(weights)
        subject_center = np.average(weighted_points, axis=0, weights=weights)
        subject_center = tuple(map(int, subject_center))

        self.logger.debug(f"Face/chest centering: {len(face_points)} face points, {len(chest_points)} chest points")

        return self._perform_centering(
            image, subject_center, detection, target_size, 'face_chest_based'
        )

    def _center_by_keypoints(
        self, 
        image: np.ndarray, 
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on keypoints (preferred method).
        
        Args:
            image: Input image
            detection: Detection with keypoints
            target_size: Target output size
            
        Returns:
            CenteringResult
        """
        keypoints = detection['keypoints']
        
        # Calculate subject center based on important keypoints
        important_points = []
        
        # Head area (highest priority)
        head_points = ['nose', 'left_eye', 'right_eye']
        head_coords = [keypoints[kp] for kp in head_points if kp in keypoints]
        
        if head_coords:
            head_center = np.mean(head_coords, axis=0)
            important_points.append(head_center)
        
        # Shoulder area (medium priority)
        shoulder_points = ['left_shoulder', 'right_shoulder']
        shoulder_coords = [keypoints[kp] for kp in shoulder_points if kp in keypoints]
        
        if shoulder_coords:
            shoulder_center = np.mean(shoulder_coords, axis=0)
            important_points.append(shoulder_center)
        
        # Hip area (lower priority)
        hip_points = ['left_hip', 'right_hip']
        hip_coords = [keypoints[kp] for kp in hip_points if kp in keypoints]
        
        if hip_coords:
            hip_center = np.mean(hip_coords, axis=0)
            important_points.append(hip_center)
        
        if not important_points:
            # Fallback to bbox method if no keypoints available
            return self._center_by_bbox(image, detection, target_size)
        
        # Calculate weighted center (head gets more weight)
        weights = [3.0, 2.0, 1.0][:len(important_points)]  # Head, shoulders, hips
        subject_center = np.average(important_points, axis=0, weights=weights)
        subject_center = tuple(map(int, subject_center))
        
        return self._perform_centering(
            image, subject_center, detection, target_size, 'keypoint_based'
        )
    
    def _center_by_bbox(
        self, 
        image: np.ndarray, 
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on bounding box.
        
        Args:
            image: Input image
            detection: Detection with bounding box
            target_size: Target output size
            
        Returns:
            CenteringResult
        """
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        
        # Use center of bounding box
        subject_center = ((x1 + x2) // 2, (y1 + y2) // 2)
        
        return self._perform_centering(
            image, subject_center, detection, target_size, 'bbox_based'
        )
    
    def _center_by_mass(
        self, 
        image: np.ndarray, 
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]] = None
    ) -> CenteringResult:
        """Center subject based on center of mass within bounding box.
        
        Args:
            image: Input image
            detection: Detection with bounding box
            target_size: Target output size
            
        Returns:
            CenteringResult
        """
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        
        # Extract region of interest
        roi = image[y1:y2, x1:x2]
        
        # Convert to grayscale for center of mass calculation
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        
        # Calculate center of mass
        moments = cv2.moments(gray_roi)
        if moments['m00'] != 0:
            cx = int(moments['m10'] / moments['m00']) + x1
            cy = int(moments['m01'] / moments['m00']) + y1
            subject_center = (cx, cy)
        else:
            # Fallback to bbox center
            subject_center = ((x1 + x2) // 2, (y1 + y2) // 2)
        
        return self._perform_centering(
            image, subject_center, detection, target_size, 'center_of_mass'
        )

    def _perform_centering(
        self,
        image: np.ndarray,
        subject_center: Tuple[int, int],
        detection: Dict[str, Any],
        target_size: Optional[Tuple[int, int]],
        method: str
    ) -> CenteringResult:
        """Perform the actual centering operation.

        Args:
            image: Input image
            subject_center: Center point of the subject
            detection: Original detection data
            target_size: Target output size
            method: Method used for centering

        Returns:
            CenteringResult
        """
        height, width = image.shape[:2]
        subject_x, subject_y = subject_center

        # Get target position from config
        target_pos = self.config.target_position
        margin_ratio = self.config.margin_ratio

        # Determine output size
        if target_size is None:
            output_width, output_height = width, height
        else:
            output_width, output_height = target_size

        # Calculate target center in output image
        target_center_x = int(output_width * target_pos[0])
        target_center_y = int(output_height * target_pos[1])

        # Calculate crop box to center subject at target position
        crop_x1 = subject_x - target_center_x
        crop_y1 = subject_y - target_center_y
        crop_x2 = crop_x1 + output_width
        crop_y2 = crop_y1 + output_height

        # Ensure crop box is within image bounds with margin
        bbox = detection['bbox']
        subject_width = bbox[2] - bbox[0]
        subject_height = bbox[3] - bbox[1]

        # Calculate minimum margins
        min_margin_x = int(subject_width * margin_ratio)
        min_margin_y = int(subject_height * margin_ratio)

        # Adjust crop box if it goes outside image bounds
        if crop_x1 < min_margin_x:
            offset = min_margin_x - crop_x1
            crop_x1 += offset
            crop_x2 += offset

        if crop_y1 < min_margin_y:
            offset = min_margin_y - crop_y1
            crop_y1 += offset
            crop_y2 += offset

        if crop_x2 > width - min_margin_x:
            offset = crop_x2 - (width - min_margin_x)
            crop_x1 -= offset
            crop_x2 -= offset

        if crop_y2 > height - min_margin_y:
            offset = crop_y2 - (height - min_margin_y)
            crop_y1 -= offset
            crop_y2 -= offset

        # Final bounds check
        crop_x1 = max(0, crop_x1)
        crop_y1 = max(0, crop_y1)
        crop_x2 = min(width, crop_x2)
        crop_y2 = min(height, crop_y2)

        # Crop the image
        cropped_image = image[crop_y1:crop_y2, crop_x1:crop_x2]

        # Resize if target size specified and different from crop size
        if target_size and (cropped_image.shape[1] != output_width or cropped_image.shape[0] != output_height):
            cropped_image = cv2.resize(cropped_image, (output_width, output_height), interpolation=cv2.INTER_LANCZOS4)

        # Calculate final target center in cropped image
        final_target_center = (target_center_x, target_center_y)

        # Calculate confidence based on how well we achieved the centering
        actual_subject_x = subject_x - crop_x1
        actual_subject_y = subject_y - crop_y1

        if target_size:
            # Scale coordinates if image was resized
            scale_x = output_width / (crop_x2 - crop_x1)
            scale_y = output_height / (crop_y2 - crop_y1)
            actual_subject_x = int(actual_subject_x * scale_x)
            actual_subject_y = int(actual_subject_y * scale_y)

        # Calculate distance from target
        distance = np.sqrt((actual_subject_x - target_center_x)**2 + (actual_subject_y - target_center_y)**2)
        max_distance = np.sqrt(output_width**2 + output_height**2)
        confidence = max(0.0, 1.0 - (distance / max_distance))

        self.logger.debug(f"Centering completed: method={method}, confidence={confidence:.3f}")

        return CenteringResult(
            cropped_image=cropped_image,
            crop_box=(crop_x1, crop_y1, crop_x2, crop_y2),
            subject_center=subject_center,
            target_center=final_target_center,
            confidence=confidence,
            method_used=method
        )

    def visualize_centering(self, result: CenteringResult) -> np.ndarray:
        """Visualize the centering result.

        Args:
            result: CenteringResult to visualize

        Returns:
            Image with centering visualization
        """
        vis_image = result.cropped_image.copy()

        # Draw target center
        target_x, target_y = result.target_center
        cv2.circle(vis_image, (target_x, target_y), 10, (0, 255, 0), 2)
        cv2.putText(vis_image, "Target", (target_x + 15, target_y),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # Draw crosshairs at target center
        cv2.line(vis_image, (target_x - 20, target_y), (target_x + 20, target_y), (0, 255, 0), 1)
        cv2.line(vis_image, (target_x, target_y - 20), (target_x, target_y + 20), (0, 255, 0), 1)

        # Add confidence text
        cv2.putText(vis_image, f"Confidence: {result.confidence:.2f}",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(vis_image, f"Method: {result.method_used}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return vis_image
